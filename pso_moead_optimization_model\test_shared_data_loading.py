#!/usr/bin/env python3
"""
测试共享数据加载功能
验证MOEAD算法中不再重复加载实际温度序列样本
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_shared_data_loading():
    """测试共享数据加载功能"""
    try:
        logger.info("=" * 60)
        logger.info("开始测试共享数据加载功能")
        logger.info("=" * 60)

        # 导入必要的模块
        from src.moead_optimizer import MOEADOptimizer
        from src.moead_fitness_evaluator import ClassificationBasedFitnessEvaluator

        config_path = "config/config.yaml"

        logger.info("\n1. 创建MOEA/D优化器（第一次数据加载）")
        optimizer = MOEADOptimizer(config_path)

        # 预先加载数据到共享实例中
        logger.info("预先加载数据到共享的业务数据分析器中...")
        optimizer.business_analyzer.load_all_temperature_data()

        logger.info("\n2. 使用共享实例创建适应度评估器（应该使用缓存数据）")
        fitness_evaluator = ClassificationBasedFitnessEvaluator(
            config_path,
            shared_business_analyzer=optimizer.business_analyzer
        )

        logger.info("\n3. 测试目标函数调用（验证是否使用缓存数据）")
        import numpy as np
        test_sequence = np.random.uniform(20, 150, 1000)  # 创建测试序列

        logger.info("调用目标函数1（统计偏差）...")
        f1 = fitness_evaluator.multi_obj_functions.objective_1_statistical_deviation(test_sequence)

        logger.info("调用目标函数2（模式匹配）...")
        f2 = fitness_evaluator.multi_obj_functions.objective_2_pattern_matching(test_sequence)

        logger.info("\n4. 验证数据共享状态")
        if (optimizer.business_analyzer is not None and
            fitness_evaluator.multi_obj_functions.business_analyzer is not None):

            # 检查是否是同一个实例
            is_same_instance = (optimizer.business_analyzer is
                              fitness_evaluator.multi_obj_functions.business_analyzer)

            logger.info(f"业务数据分析器是否为同一实例: {is_same_instance}")

            # 检查数据是否已加载
            optimizer_data_loaded = hasattr(optimizer.business_analyzer, 'temperature_sequences') and \
                                  bool(optimizer.business_analyzer.temperature_sequences)

            evaluator_data_loaded = hasattr(fitness_evaluator.multi_obj_functions.business_analyzer, 'temperature_sequences') and \
                                  bool(fitness_evaluator.multi_obj_functions.business_analyzer.temperature_sequences)

            logger.info(f"优化器中数据已加载: {optimizer_data_loaded}")
            logger.info(f"评估器中数据已加载: {evaluator_data_loaded}")

            if optimizer_data_loaded and evaluator_data_loaded:
                optimizer_sample_count = len(optimizer.business_analyzer.temperature_sequences)
                evaluator_sample_count = len(fitness_evaluator.multi_obj_functions.business_analyzer.temperature_sequences)

                logger.info(f"优化器中样本数量: {optimizer_sample_count}")
                logger.info(f"评估器中样本数量: {evaluator_sample_count}")
                logger.info(f"目标函数1结果: {f1:.4f}")
                logger.info(f"目标函数2结果: {f2:.4f}")

                if is_same_instance and optimizer_sample_count == evaluator_sample_count:
                    logger.info("✅ 测试成功：数据共享正常，避免了重复加载")
                    return True
                else:
                    logger.error("❌ 测试失败：数据未正确共享")
                    return False
            else:
                logger.error("❌ 测试失败：数据未正确加载")
                return False
        else:
            logger.error("❌ 测试失败：业务数据分析器未正确初始化")
            return False

    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        return False

def test_old_vs_new_approach():
    """对比旧方法和新方法的数据加载次数"""
    try:
        logger.info("\n" + "=" * 60)
        logger.info("对比测试：旧方法 vs 新方法")
        logger.info("=" * 60)
        
        # 导入必要的模块
        from src.moead_optimizer import MOEADOptimizer
        from src.moead_fitness_evaluator import ClassificationBasedFitnessEvaluator
        
        config_path = "config/config.yaml"
        
        logger.info("\n【新方法测试】使用共享实例")
        logger.info("-" * 40)
        
        # 新方法：使用共享实例
        optimizer = MOEADOptimizer(config_path)
        fitness_evaluator_shared = ClassificationBasedFitnessEvaluator(
            config_path, 
            shared_business_analyzer=optimizer.business_analyzer
        )
        
        logger.info("\n【旧方法测试】独立创建实例")
        logger.info("-" * 40)
        
        # 旧方法：独立创建实例（模拟原来的行为）
        fitness_evaluator_independent = ClassificationBasedFitnessEvaluator(config_path)
        
        logger.info("\n【结果对比】")
        logger.info("-" * 40)
        
        # 检查实例是否相同
        shared_same = (optimizer.business_analyzer is 
                      fitness_evaluator_shared.multi_obj_functions.business_analyzer)
        
        independent_same = (optimizer.business_analyzer is 
                           fitness_evaluator_independent.multi_obj_functions.business_analyzer)
        
        logger.info(f"新方法 - 共享实例: {shared_same}")
        logger.info(f"旧方法 - 共享实例: {independent_same}")
        
        if shared_same and not independent_same:
            logger.info("✅ 对比测试成功：新方法实现了数据共享，旧方法会重复加载")
            return True
        else:
            logger.error("❌ 对比测试失败：数据共享机制未按预期工作")
            return False
            
    except Exception as e:
        logger.error(f"对比测试过程中出错: {e}")
        return False

if __name__ == "__main__":
    logger.info("开始MOEAD数据共享测试")
    
    # 测试1：基本共享功能
    test1_result = test_shared_data_loading()
    
    # 测试2：对比新旧方法
    test2_result = test_old_vs_new_approach()
    
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"基本共享功能测试: {'通过' if test1_result else '失败'}")
    logger.info(f"新旧方法对比测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！MOEAD数据共享功能正常工作")
        sys.exit(0)
    else:
        logger.error("❌ 部分测试失败，请检查实现")
        sys.exit(1)
