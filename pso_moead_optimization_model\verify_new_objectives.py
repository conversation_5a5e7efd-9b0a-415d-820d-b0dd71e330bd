#!/usr/bin/env python3
"""
简单验证新目标函数的实现
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def main():
    try:
        print("开始验证新目标函数...")
        
        # 测试温度稳定性目标函数（不依赖分类器）
        print("1. 测试温度稳定性目标函数")
        
        # 创建测试序列
        stable_sequence = np.full(1000, 100.0)  # 完全稳定的序列
        volatile_sequence = 100 + 20 * np.sin(np.linspace(0, 20*np.pi, 1000))  # 波动序列
        
        # 计算稳定性指标
        def calculate_stability(sequence):
            diff1 = np.diff(sequence)
            change_rate_std = np.std(diff1)
            change_rate_var = np.var(diff1)
            
            diff2 = np.diff(sequence, n=2)
            acceleration_mean = np.mean(np.abs(diff2))
            
            smoothness = 1.0 / (1.0 + change_rate_var)
            sign_changes = np.sum(np.diff(np.sign(diff1)) != 0)
            continuity_penalty = sign_changes / len(diff1) if len(diff1) > 0 else 0
            
            stability_score = (
                0.30 * min(1.0, change_rate_std / 10.0) +
                0.25 * min(1.0, change_rate_std / 10.0) +  # 简化波动性计算
                0.20 * min(1.0, acceleration_mean / 5.0) +
                0.15 * continuity_penalty +
                0.10 * (1.0 - smoothness)
            )
            
            return stability_score
        
        stable_score = calculate_stability(stable_sequence)
        volatile_score = calculate_stability(volatile_sequence)
        
        print(f"稳定序列稳定性评分: {stable_score:.4f}")
        print(f"波动序列稳定性评分: {volatile_score:.4f}")
        
        if stable_score < volatile_score:
            print("✅ 稳定性目标函数逻辑正确：稳定序列得分更低（更好）")
        else:
            print("❌ 稳定性目标函数逻辑可能有问题")
        
        print("\n2. 测试新目标函数类的导入")
        try:
            from src.new_objective_functions import NewObjectiveFunctions
            print("✅ NewObjectiveFunctions类导入成功")
            
            # 测试实例化
            obj = NewObjectiveFunctions()
            print("✅ NewObjectiveFunctions实例化成功")
            
            # 测试稳定性目标函数
            test_sequence = np.linspace(20, 150, 1000)
            stability_result = obj.objective_3_temperature_stability(test_sequence)
            print(f"✅ 稳定性目标函数计算成功: {stability_result:.4f}")
            
        except Exception as e:
            print(f"❌ NewObjectiveFunctions测试失败: {e}")
        
        print("\n3. 测试MOEAD适应度评估器集成")
        try:
            from src.moead_fitness_evaluator import ClassificationBasedFitnessEvaluator
            print("✅ ClassificationBasedFitnessEvaluator导入成功")
            
            # 测试实例化
            evaluator = ClassificationBasedFitnessEvaluator()
            print("✅ ClassificationBasedFitnessEvaluator实例化成功")
            
            # 测试目标函数描述
            descriptions = evaluator.get_objective_descriptions()
            print("✅ 目标函数描述获取成功:")
            for key, desc in descriptions.items():
                print(f"  {key}: {desc}")
            
        except Exception as e:
            print(f"❌ MOEAD适应度评估器测试失败: {e}")
        
        print("\n4. 测试MOEAD多目标函数")
        try:
            evaluator = ClassificationBasedFitnessEvaluator()
            multi_obj_func = evaluator.get_multi_objective_function_for_moead()
            print("✅ MOEAD多目标函数获取成功")
            
            # 测试函数调用
            test_sequence = np.linspace(20, 150, 1000)
            objectives = multi_obj_func(test_sequence)
            print(f"✅ MOEAD多目标函数计算成功: {objectives}")
            
            if isinstance(objectives, list) and len(objectives) == 3:
                print("✅ 目标函数返回格式正确：3个目标值")
            else:
                print("❌ 目标函数返回格式不正确")
                
        except Exception as e:
            print(f"❌ MOEAD多目标函数测试失败: {e}")
        
        print("\n=" * 50)
        print("新目标函数验证完成")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"验证过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("🎉 验证成功！新目标函数实现正确")
    else:
        print("❌ 验证失败，请检查实现")
