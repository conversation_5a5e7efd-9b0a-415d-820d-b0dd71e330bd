#!/usr/bin/env python3
"""
测试新的目标函数实现
验证三个新目标函数的正确性和功能
"""

import sys
import os
import logging
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_new_objectives():
    """测试新的目标函数"""
    try:
        logger.info("=" * 60)
        logger.info("开始测试新的目标函数")
        logger.info("=" * 60)
        
        # 导入必要的模块
        from src.new_objective_functions import NewObjectiveFunctions
        
        config_path = "config/config.yaml"
        
        logger.info("1. 创建新目标函数实例")
        new_objectives = NewObjectiveFunctions(config_path)
        
        logger.info("2. 创建测试温度序列")
        # 创建一个模拟的温度序列（上升趋势，带一些噪声）
        sequence_length = 1000
        base_temp = np.linspace(20, 150, sequence_length)
        noise = np.random.normal(0, 2, sequence_length)
        test_sequence = base_temp + noise
        
        logger.info(f"测试序列长度: {len(test_sequence)}")
        logger.info(f"温度范围: {test_sequence.min():.2f}°C - {test_sequence.max():.2f}°C")
        
        logger.info("3. 测试单个目标函数")
        
        # 测试目标函数1：Label1预测
        logger.info("测试目标函数1：Label1分类器预测")
        try:
            f1 = new_objectives.objective_1_label1_prediction(test_sequence)
            logger.info(f"✅ 目标函数1结果: {f1:.4f}")
        except Exception as e:
            logger.error(f"❌ 目标函数1测试失败: {e}")
        
        # 测试目标函数2：Label2预测
        logger.info("测试目标函数2：Label2分类器预测")
        try:
            f2 = new_objectives.objective_2_label2_prediction(test_sequence)
            logger.info(f"✅ 目标函数2结果: {f2:.4f}")
        except Exception as e:
            logger.error(f"❌ 目标函数2测试失败: {e}")
        
        # 测试目标函数3：温度稳定性
        logger.info("测试目标函数3：温度变化稳定性")
        try:
            f3 = new_objectives.objective_3_temperature_stability(test_sequence)
            logger.info(f"✅ 目标函数3结果: {f3:.4f}")
        except Exception as e:
            logger.error(f"❌ 目标函数3测试失败: {e}")
        
        logger.info("4. 测试所有目标函数")
        try:
            objectives = new_objectives.evaluate_all_objectives(test_sequence)
            logger.info(f"✅ 所有目标函数结果: {objectives}")
            
            # 测试个别目标函数
            individual_objectives = new_objectives.evaluate_individual_objectives(test_sequence)
            logger.info(f"✅ 个别目标函数结果: {individual_objectives}")
            
            # 测试目标函数描述
            descriptions = new_objectives.get_objective_descriptions()
            logger.info("✅ 目标函数描述:")
            for key, desc in descriptions.items():
                logger.info(f"  {key}: {desc}")
                
        except Exception as e:
            logger.error(f"❌ 综合测试失败: {e}")
        
        logger.info("5. 测试不同类型的温度序列")
        
        # 测试稳定序列
        stable_sequence = np.full(1000, 100.0) + np.random.normal(0, 0.1, 1000)
        logger.info("测试稳定序列:")
        try:
            stable_objectives = new_objectives.evaluate_all_objectives(stable_sequence)
            logger.info(f"  稳定序列目标函数值: {stable_objectives}")
        except Exception as e:
            logger.error(f"  稳定序列测试失败: {e}")
        
        # 测试波动序列
        volatile_sequence = 100 + 20 * np.sin(np.linspace(0, 20*np.pi, 1000)) + np.random.normal(0, 5, 1000)
        logger.info("测试波动序列:")
        try:
            volatile_objectives = new_objectives.evaluate_all_objectives(volatile_sequence)
            logger.info(f"  波动序列目标函数值: {volatile_objectives}")
        except Exception as e:
            logger.error(f"  波动序列测试失败: {e}")
        
        logger.info("6. 测试缓存功能")
        try:
            # 第一次计算
            start_time = time.time()
            objectives1 = new_objectives.evaluate_all_objectives(test_sequence)
            time1 = time.time() - start_time
            
            # 第二次计算（应该使用缓存）
            start_time = time.time()
            objectives2 = new_objectives.evaluate_all_objectives(test_sequence)
            time2 = time.time() - start_time
            
            logger.info(f"第一次计算时间: {time1:.4f}秒")
            logger.info(f"第二次计算时间: {time2:.4f}秒")
            logger.info(f"结果一致性: {np.allclose(objectives1, objectives2)}")
            
            if time2 < time1 * 0.5:  # 缓存应该显著加快速度
                logger.info("✅ 缓存功能正常工作")
            else:
                logger.warning("⚠️ 缓存功能可能未正常工作")
                
        except Exception as e:
            logger.error(f"❌ 缓存测试失败: {e}")
        
        logger.info("=" * 60)
        logger.info("新目标函数测试完成")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_moead_integration():
    """测试与MOEAD的集成"""
    try:
        logger.info("=" * 60)
        logger.info("测试与MOEAD的集成")
        logger.info("=" * 60)
        
        # 导入MOEAD相关模块
        from src.moead_fitness_evaluator import ClassificationBasedFitnessEvaluator
        
        config_path = "config/config.yaml"
        
        logger.info("1. 创建适应度评估器")
        fitness_evaluator = ClassificationBasedFitnessEvaluator(config_path)
        
        logger.info("2. 获取MOEAD多目标函数")
        multi_objective_function = fitness_evaluator.get_multi_objective_function_for_moead()
        
        logger.info("3. 测试多目标函数")
        # 创建测试序列
        test_sequence = np.linspace(20, 150, 1000) + np.random.normal(0, 1, 1000)
        
        try:
            objectives = multi_objective_function(test_sequence)
            logger.info(f"✅ MOEAD多目标函数结果: {objectives}")
            
            # 验证结果格式
            if isinstance(objectives, list) and len(objectives) == 3:
                logger.info("✅ 结果格式正确：3个目标函数值")
                
                # 验证所有值都是数值
                if all(isinstance(obj, (int, float)) for obj in objectives):
                    logger.info("✅ 所有目标函数值都是数值类型")
                else:
                    logger.error("❌ 目标函数值类型不正确")
                    
            else:
                logger.error("❌ 结果格式不正确")
                
        except Exception as e:
            logger.error(f"❌ MOEAD多目标函数测试失败: {e}")
        
        logger.info("4. 测试目标函数描述")
        try:
            descriptions = fitness_evaluator.get_objective_descriptions()
            logger.info("✅ 目标函数描述:")
            for key, desc in descriptions.items():
                logger.info(f"  {key}: {desc}")
        except Exception as e:
            logger.error(f"❌ 目标函数描述测试失败: {e}")
        
        logger.info("=" * 60)
        logger.info("MOEAD集成测试完成")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"MOEAD集成测试过程中出错: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import time
    
    logger.info("开始新目标函数测试")
    
    # 测试1：基本功能测试
    test1_result = test_new_objectives()
    
    # 测试2：MOEAD集成测试
    test2_result = test_moead_integration()
    
    logger.info("\n" + "=" * 60)
    logger.info("测试总结")
    logger.info("=" * 60)
    logger.info(f"基本功能测试: {'通过' if test1_result else '失败'}")
    logger.info(f"MOEAD集成测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        logger.info("🎉 所有测试通过！新目标函数正常工作")
        sys.exit(0)
    else:
        logger.error("❌ 部分测试失败，请检查实现")
        sys.exit(1)
