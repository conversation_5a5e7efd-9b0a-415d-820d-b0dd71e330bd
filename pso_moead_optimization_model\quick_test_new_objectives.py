#!/usr/bin/env python3
"""
快速测试新目标函数的实现
"""

import sys
import os
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def main():
    print("快速测试新目标函数...")
    
    try:
        # 测试温度稳定性目标函数（不依赖分类器）
        print("1. 测试温度稳定性目标函数")
        
        from src.new_objective_functions import NewObjectiveFunctions
        
        # 创建实例
        obj_func = NewObjectiveFunctions()
        print("✅ NewObjectiveFunctions实例创建成功")
        
        # 创建测试序列
        test_sequence = np.linspace(20, 150, 1000)
        
        # 测试稳定性目标函数
        stability_result = obj_func.objective_3_temperature_stability(test_sequence)
        print(f"✅ 稳定性目标函数计算成功: {stability_result:.4f}")
        
        # 测试所有目标函数
        all_objectives = obj_func.evaluate_all_objectives(test_sequence)
        print(f"✅ 所有目标函数计算成功: {all_objectives}")
        
        # 测试目标函数描述
        descriptions = obj_func.get_objective_descriptions()
        print("✅ 目标函数描述:")
        for key, desc in descriptions.items():
            print(f"  {key}: {desc}")
        
        print("\n2. 测试MOEAD集成")
        
        from src.moead_fitness_evaluator import ClassificationBasedFitnessEvaluator
        
        # 创建适应度评估器
        evaluator = ClassificationBasedFitnessEvaluator()
        print("✅ ClassificationBasedFitnessEvaluator创建成功")
        
        # 获取MOEAD多目标函数
        multi_obj_func = evaluator.get_multi_objective_function_for_moead()
        print("✅ MOEAD多目标函数获取成功")
        
        # 测试多目标函数调用
        moead_objectives = multi_obj_func(test_sequence)
        print(f"✅ MOEAD多目标函数计算成功: {moead_objectives}")
        
        # 验证结果格式
        if isinstance(moead_objectives, list) and len(moead_objectives) == 3:
            print("✅ 结果格式正确：3个目标函数值")
        else:
            print("❌ 结果格式不正确")
        
        # 测试目标函数描述
        moead_descriptions = evaluator.get_objective_descriptions()
        print("✅ MOEAD目标函数描述:")
        for key, desc in moead_descriptions.items():
            print(f"  {key}: {desc}")
        
        print("\n🎉 所有测试通过！新目标函数实现正确")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
